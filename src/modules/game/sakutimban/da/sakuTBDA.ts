import {DataController} from '../../../../base/baseController';
import {
  GameQuizQuestionAPI,
  GetQuestionsRequest,
  GetQuestionsResponse,
  ApiResponse,
  SakuTBError,
  GameConfig,
} from '../types/sakuTBTypes';

export class SakuTBDA {
  private questionController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuizQuestion');
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách câu hỏi từ bảng GameQuizQuestion
   * @param gameId ID của game SakuTB
   * @param stage Stage hiện tại (mặc định = 1)
   * @returns Promise<GameQuizQuestionAPI[]>
   */
  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number = 1,
    competenceId: string,
  ): Promise<GameQuizQuestionAPI[]> {
    try {
      const response: GetQuestionsResponse =
        await this.questionController.getPatternList({
          query: `@GameId: {${gameId}} @Stage: [${stage}] @GameCompetenceId: {${competenceId}}`,
          pattern: {
            GameId: ['Id', 'Name', 'Time'],
          },
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        });
      console.log(`[SakuTBDA] API Response:`, response);

      if (response.code === 200) {
        const questions = response.data || [];
        // Validate questions
        const validQuestions = this.validateQuestions(questions);
        return validQuestions;
      } else {
        throw new Error(
          `API Error: ${response.message} (Code: ${response.code})`,
        );
      }
    } catch (error) {
      console.error('[SakuTBDA] Error fetching questions:', error);
      throw this.createError(
        'API_ERROR',
        'Failed to fetch questions from API',
        error,
      );
    }
  }

  /**
   * Lấy câu hỏi theo ID cụ thể
   * @param questionId ID của câu hỏi
   * @returns Promise<GameQuizQuestionAPI | null>
   */
  async getQuestionById(
    questionId: string,
  ): Promise<GameQuizQuestionAPI | null> {
    try {
      console.log(`[SakuTBDA] Fetching question by ID: ${questionId}`);

      const response = await this.questionController.getById(questionId);

      if (response.code === 200 && response.data) {
        return response.data as GameQuizQuestionAPI;
      }

      return null;
    } catch (error) {
      console.error('[SakuTBDA] Error fetching question by ID:', error);
      throw this.createError(
        'API_ERROR',
        'Failed to fetch question by ID',
        error,
      );
    }
  }

  /**
   * Lấy danh sách tất cả stages có sẵn cho game
   * @param gameId ID của game
   * @returns Promise<number[]>
   */
  async getAvailableStages(gameId: string): Promise<number[]> {
    try {
      console.log(`[SakuTBDA] Fetching available stages for GameId: ${gameId}`);

      const response: ApiResponse<GameQuizQuestionAPI> =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} AND @IsActive: true`,
          sortby: {BY: 'Stage', DIRECTION: 'ASC'},
        });

      if (response.code === 200) {
        const questions = response.data || [];
        // Lấy danh sách stages unique
        const stages = [...new Set(questions.map(q => q.Stage))].sort(
          (a, b) => a - b,
        );
        console.log(`[SakuTBDA] Available stages:`, stages);
        return stages;
      }

      return [1]; // Default stage
    } catch (error) {
      console.error('[SakuTBDA] Error fetching stages:', error);
      return [1]; // Fallback to stage 1
    }
  }

  /**
   * Validate questions data
   * @param questions Raw questions from API
   * @returns Valid questions
   */
  private validateQuestions(
    questions: GameQuizQuestionAPI[],
  ): GameQuizQuestionAPI[] {
    return questions.filter(question => {
      try {
        // Check required fields
        if (
          !question.Id ||
          !question.Content ||
          !question.Options ||
          !question.CorrectAnswerIndex
        ) {
          console.warn(
            `[SakuTBDA] Question ${question.Id} missing required fields`,
          );
          return false;
        }

        // Validate Options JSON
        const options = JSON.parse(question.Options);
        if (!Array.isArray(options) || options.length !== 10) {
          console.warn(
            `[SakuTBDA] Question ${question.Id} has invalid Options format`,
          );
          return false;
        }

        // Validate CorrectAnswerIndex JSON
        const correctAnswers = JSON.parse(question.CorrectAnswerIndex);
        if (!Array.isArray(correctAnswers) || correctAnswers.length !== 5) {
          console.warn(
            `[SakuTBDA] Question ${question.Id} has invalid CorrectAnswerIndex format`,
          );
          return false;
        }

        // Validate each correct answer pair
        for (const pair of correctAnswers) {
          const [leftIndex, rightIndex] = pair.split(',').map(Number);
          if (
            isNaN(leftIndex) ||
            isNaN(rightIndex) ||
            leftIndex < 1 ||
            leftIndex > 5 ||
            rightIndex < 6 ||
            rightIndex > 10
          ) {
            console.warn(
              `[SakuTBDA] Question ${question.Id} has invalid answer pair: ${pair}`,
            );
            return false;
          }
        }

        return true;
      } catch (error) {
        console.warn(
          `[SakuTBDA] Question ${question.Id} validation failed:`,
          error,
        );
        return false;
      }
    });
  }

  /**
   * Create standardized error
   * @param type Error type
   * @param message Error message
   * @param details Additional details
   * @returns SakuTBError
   */
  private createError(
    type: SakuTBError['type'],
    message: string,
    details?: any,
  ): SakuTBError {
    return {
      type,
      message,
      details,
    };
  }
  static async getGameConfig(gameId: string): Promise<any> {
    try {
      const controller = new DataController('GameConfig');
      const response = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });
      if (
        response.code !== 200 ||
        !response.data ||
        response.data.length === 0
      ) {
        throw new Error(
          'No game config found or API returned unsuccessful response',
        );
      }
      const configData = response.data[0];
      return {
        scorePerLife: configData.Score,
        maxLives: configData.LifeCount,
        timeLimit: configData.Time,
        bonusScore: configData.Bonus,
        isActive: true,
      };
    } catch (error) {
      console.error('[GameConfigDA] Error loading game config:', error);
      throw error;
    }
  }
  static calculateScore(
    config: GameConfig,
    livesRemaining: number,
    totalLives: number,
  ): number {
    const baseScore = livesRemaining * config.scorePerLife;

    // Bonus nếu không mất mạng nào
    const bonus = livesRemaining === totalLives ? config.bonusScore : 0;

    const finalScore = baseScore + bonus;

    return finalScore;
  }
}
