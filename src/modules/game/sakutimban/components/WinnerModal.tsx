import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Dimensions,
  ImageBackground,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';

const {width, height} = Dimensions.get('window');

interface WinnerModalProps {
  visible: boolean;
  onClose: () => void;
  restartGame: () => void;
  score?: number;
  totalLives?: number;
}

const WinnerModal = ({
  visible,
  onClose,
  restartGame,
  score = 0,
  totalLives = 3,
}: WinnerModalProps) => {
  const navigation = useNavigation<any>();

  const handleRestart = () => {
    onClose();
    restartGame();
  };

  const handleGoHome = () => {
    onClose();
    navigation.goBack();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Tiêu đề Winner */}
          <Text style={styles.winnerText}>Chúc mừng!</Text>
          <Text style={styles.subTitle}>Bạn đã hoàn thành tất cả câu hỏi!</Text>

          {/* Hiển thị điểm số */}
          <View style={styles.scoreContainer}>
            <Image
              source={require('../assets/2_bird.png')}
              style={styles.celebrationIcon}
            />
            <View style={styles.scoreDetails}>
              <Text style={styles.scoreLabel}>Số mạng còn lại:</Text>
              <View style={styles.livesDisplay}>
                {Array.from({length: totalLives}, (_, index) => (
                  <Image
                    key={index}
                    source={require('../../assets/heart.png')}
                    style={[
                      styles.heartIcon,
                      index >= score && styles.heartIconEmpty
                    ]}
                  />
                ))}
              </View>
              <Text style={styles.scoreText}>Điểm đạt được: {score}/{totalLives}</Text>
              {score === totalLives && (
                <Text style={styles.perfectText}>🎉 Hoàn hảo! 🎉</Text>
              )}
            </View>
          </View>

          {/* Hình ảnh chim vui mừng */}
          <View style={styles.birdContainer}>
            <Image
              source={require('../assets/female_bird.png')}
              style={styles.birdImage}
            />
            <Image
              source={require('../assets/male_bird.png')}
              style={styles.birdImage}
            />
          </View>

          {/* Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.restartButton]}
              onPress={handleRestart}>
              <Text style={styles.buttonText}>Chơi lại</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.homeButton]}
              onPress={handleGoHome}>
              <Text style={styles.buttonText}>Về trang chủ</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#F1D1A6',
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    width: width * 0.85,
    maxHeight: height * 0.8,
    borderWidth: 3,
    borderColor: '#D4A574',
  },
  winnerText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#112164',
    marginBottom: 10,
    textAlign: 'center',
  },
  subTitle: {
    fontSize: 18,
    color: '#112164',
    marginBottom: 20,
    textAlign: 'center',
  },
  scoreContainer: {
    alignItems: 'center',
    marginBottom: 20,
    backgroundColor: '#FCF8E8',
    padding: 15,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#D4A574',
    width: '100%',
  },
  celebrationIcon: {
    width: 60,
    height: 60,
    marginBottom: 10,
  },
  scoreDetails: {
    alignItems: 'center',
    width: '100%',
  },
  scoreLabel: {
    fontSize: 16,
    color: '#112164',
    marginBottom: 8,
    fontWeight: '600',
  },
  livesDisplay: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    gap: 5,
  },
  heartIcon: {
    width: 24,
    height: 24,
    tintColor: '#FF4444',
  },
  heartIconEmpty: {
    tintColor: '#CCCCCC',
  },
  scoreText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#112164',
    marginBottom: 5,
  },
  perfectText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
    textAlign: 'center',
  },
  birdContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginBottom: 30,
    width: '100%',
  },
  birdImage: {
    width: 80,
    height: 80,
    marginHorizontal: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 15,
  },
  button: {
    flex: 1,
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
  },
  restartButton: {
    backgroundColor: '#4CAF50',
    borderColor: '#3d8b40',
  },
  homeButton: {
    backgroundColor: '#2196F3',
    borderColor: '#0d8aee',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});

export default WinnerModal;
