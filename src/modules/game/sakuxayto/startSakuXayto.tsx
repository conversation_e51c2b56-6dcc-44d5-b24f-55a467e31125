import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Alert,
  Dimensions,
  PanResponder,
  Animated,
  Easing,
  Vibration,
  Platform,
  ImageBackground,
} from 'react-native';

import CountBadge from '../components/CountQuestions';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {useDhbcHook} from '../../../redux/hook/game/dhbcHook';
import {SafeAreaView} from 'react-native-safe-area-context';

import {BottomGame} from '../components/BottomGame';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import GameOverModal from '../components/GameOverModel';
import ModelDoneLevel from '../components/ModelDoneLevel';

import {GestureHandlerRootView} from 'react-native-gesture-handler';

const {width: SCREEN_WIDTH} = Dimensions.get('window');

interface InsertZoneProps {
  onReceiveDragDrop: (word: string) => void;
  index: number;
  isActive?: boolean;
  onDropSuccess?: () => void;
}

const InsertZone = React.forwardRef<View, InsertZoneProps>(
  ({isActive, onDropSuccess}, ref) => {
    const scaleAnim = useRef(new Animated.Value(1)).current;
    const opacityAnim = useRef(new Animated.Value(0.3)).current;
    const pulseAnim = useRef(new Animated.Value(1)).current;
    const successScaleAnim = useRef(new Animated.Value(1)).current;

    // Animation for when zone becomes active (word being dragged over)
    React.useEffect(() => {
      if (isActive) {
        // Start hover animations with smoother easing
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 1.3,
            duration: 150,
            easing: Easing.out(Easing.back(1.2)),
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 150,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
          // Smoother pulse animation
          Animated.loop(
            Animated.sequence([
              Animated.timing(pulseAnim, {
                toValue: 1.05,
                duration: 800,
                easing: Easing.inOut(Easing.sin),
                useNativeDriver: true,
              }),
              Animated.timing(pulseAnim, {
                toValue: 1,
                duration: 800,
                easing: Easing.inOut(Easing.sin),
                useNativeDriver: true,
              }),
            ]),
          ),
        ]).start();
      } else {
        // Reset to normal state with smooth transition
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 200,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 0.4,
            duration: 200,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
        ]).start();

        // Stop pulse animation smoothly
        pulseAnim.stopAnimation();
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 100,
          easing: Easing.out(Easing.quad),
          useNativeDriver: true,
        }).start();
      }
    }, [isActive, scaleAnim, opacityAnim, pulseAnim]);

    // Success animation when word is dropped
    const triggerSuccessAnimation = React.useCallback(() => {
      // Add haptic feedback
      Vibration.vibrate(50, false);

      Animated.sequence([
        // Quick scale up with bounce
        Animated.timing(successScaleAnim, {
          toValue: 1.4,
          duration: 120,
          easing: Easing.out(Easing.back(1.5)),
          useNativeDriver: true,
        }),
        // Smooth scale back with elastic feel
        Animated.timing(successScaleAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.out(Easing.elastic(1.2)),
          useNativeDriver: true,
        }),
      ]).start(() => {
        if (onDropSuccess) {
          onDropSuccess();
        }
      });
    }, [successScaleAnim, onDropSuccess]);

    // Store the success animation function in a ref so parent can access it
    const successAnimRef = useRef(triggerSuccessAnimation);
    successAnimRef.current = triggerSuccessAnimation;

    // Expose success animation to parent via a custom property
    React.useEffect(() => {
      if (ref && typeof ref === 'object' && ref.current) {
        (ref.current as any).triggerSuccessAnimation = successAnimRef.current;
      }
    });

    return (
      <View ref={ref} style={styles.insertZone}>
        <Animated.View
          style={[
            styles.insertZoneInner,
            isActive && styles.insertZoneActive,
            {
              transform: [
                {scale: Animated.multiply(scaleAnim, successScaleAnim)},
                {scale: pulseAnim},
              ],
              opacity: opacityAnim,
            },
          ]}>
          {/* Hiển thị khoảng trắng thay vì dấu + */}
        </Animated.View>
      </View>
    );
  },
);

// Animated Word Component for smooth sentence expansion
interface AnimatedWordProps {
  word: string;
  index: number;
  isSubmitted: boolean;
  isInserting: boolean;
  insertIndex: number;
}

const AnimatedWord = React.memo(
  ({word, index, isSubmitted, isInserting, insertIndex}: AnimatedWordProps) => {
    const translateX = useRef(new Animated.Value(0)).current;
    const scale = useRef(new Animated.Value(1)).current;

    React.useEffect(() => {
      if (isInserting) {
        // Animate words to make space for new word
        const shouldMoveRight = index >= insertIndex;
        const moveDistance = shouldMoveRight ? 60 : 0; // Approximate word width + margin

        Animated.parallel([
          Animated.timing(translateX, {
            toValue: moveDistance,
            duration: 300,
            easing: Easing.out(Easing.back(1.1)),
            useNativeDriver: true,
          }),
          Animated.sequence([
            Animated.timing(scale, {
              toValue: 0.95,
              duration: 150,
              easing: Easing.out(Easing.quad),
              useNativeDriver: true,
            }),
            Animated.timing(scale, {
              toValue: 1,
              duration: 150,
              easing: Easing.out(Easing.back(1.2)),
              useNativeDriver: true,
            }),
          ]),
        ]).start();
      } else {
        // Reset position
        Animated.parallel([
          Animated.timing(translateX, {
            toValue: 0,
            duration: 250,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1,
            duration: 200,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
        ]).start();
      }
    }, [isInserting, insertIndex, index, translateX, scale]);

    return (
      <Animated.View
        style={[
          styles.word,
          isSubmitted && styles.wordSubmitted,
          {
            transform: [{translateX}, {scale}],
          },
        ]}>
        <Text
          style={[styles.wordText, isSubmitted && styles.wordTextSubmitted]}>
          {word}
        </Text>
      </Animated.View>
    );
  },
);

interface DraggableWordProps {
  word: string;
  used: boolean;
  onDragEnd: (word: string, x: number, y: number) => void;
  onDragStart: () => void;
  onDragMove: (x: number, y: number) => void;
}

const DraggableWord = React.memo(
  ({word, used, onDragEnd, onDragStart, onDragMove}: DraggableWordProps) => {
    const pan = useRef(new Animated.ValueXY()).current;
    const scale = useRef(new Animated.Value(1)).current;

    const panResponder = useRef(
      PanResponder.create({
        onStartShouldSetPanResponder: () => true,
        onMoveShouldSetPanResponder: () => true,
        onPanResponderGrant: () => {
          //console.log('Drag started');
          onDragStart();
          Animated.spring(scale, {
            toValue: 1.1,
            useNativeDriver: true,
          }).start();
        },
        onPanResponderMove: (_, gestureState) => {
          //console.log('Dragging:', gestureState.moveX, gestureState.moveY);
          pan.setValue({x: gestureState.dx, y: gestureState.dy});
          onDragMove(gestureState.moveX, gestureState.moveY);
        },
        onPanResponderRelease: (_, gestureState) => {
          //console.log('Drag released at:', gestureState.moveX, gestureState.moveY);
          Animated.spring(scale, {
            toValue: 1,
            useNativeDriver: true,
          }).start();
          onDragEnd(word, gestureState.moveX, gestureState.moveY);
          pan.setValue({x: 0, y: 0});
        },
        onPanResponderTerminate: () => {
          console.log('Drag terminated');
          Animated.spring(scale, {
            toValue: 1,
            useNativeDriver: true,
          }).start();
          pan.setValue({x: 0, y: 0});
        },
      }),
    ).current;

    if (used) {
      return (
        <View style={[styles.draggable, styles.usedDraggable]}>
          <Text style={styles.dragText}>{word}</Text>
        </View>
      );
    }

    return (
      <Animated.View
        {...panResponder.panHandlers}
        style={[
          styles.draggable,
          {
            transform: [{translateX: pan.x}, {translateY: pan.y}, {scale}],
          },
        ]}>
        <Text style={styles.dragText}>{word}</Text>
      </Animated.View>
    );
  },
);

interface InsertZonePosition {
  x: number;
  y: number;
  index: number;
  distance?: number;
}

const SakuXayTo = () => {
  const dhbcHook = useDhbcHook();
  const gameHook = useGameHook();
  const {totalLives, currentLives, isGameOver, messageGameOver, gem, cup} =
    useSelector((state: RootState) => state.gameStore);
  const {totalQuestion, questionDone, currentQuestion, isWinLevel} =
    useSelector((state: RootState) => state.dhbcStore);

  const [isShowKeyboard, setIsShowKeyboard] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [isCorrect, setIsCorrect] = useState<boolean>(false);
  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);

  const [sentence, setSentence] = useState(['Tôi', 'ăn', 'cơm']);
  const [draggableWords, setDraggableWords] = useState([
    'đang',
    'sẽ',
    'đã',
    'rất',
    'ngon',
  ]);
  const [usedWords, setUsedWords] = useState<Set<string>>(new Set());
  const [insertZones, setInsertZones] = useState<InsertZonePosition[]>([]);
  const [activeZoneIndex, setActiveZoneIndex] = useState<number | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isInserting, setIsInserting] = useState(false);
  const [insertingAtIndex, setInsertingAtIndex] = useState<number>(-1);
  const insertZoneRefs = useRef<View[]>([]);
  const insertZonesRef = useRef<InsertZonePosition[]>([]);

  // Animation refs for sentence expansion
  const sentenceContainerHeight = useRef(new Animated.Value(80)).current;

  const updateInsertZonePositions = useCallback(() => {
    const newZones: InsertZonePosition[] = [];
    let completed = 0;

    insertZoneRefs.current.forEach((ref, index) => {
      if (ref) {
        ref.measure((x, y, width, height, pageX, pageY) => {
          // Tính toán vị trí chính xác của insert zone
          const centerX = pageX + width / 2;
          const centerY = pageY + height / 2;

          newZones[index] = {
            x: centerX,
            y: centerY,
            index,
          };
          completed++;

          if (completed === insertZoneRefs.current.length) {
            console.log('Updated insert zones:', newZones);
            insertZonesRef.current = newZones;
            setInsertZones(newZones);
          }
        });
      }
    });
  }, []);

  // Cập nhật vị trí khi component mount và khi sentence thay đổi
  React.useLayoutEffect(() => {
    console.log('Layout effect running');
    // Thêm delay nhỏ để đảm bảo layout đã được cập nhật
    const timer = setTimeout(() => {
      updateInsertZonePositions();
    }, 100);
    return () => clearTimeout(timer);
  }, [sentence, updateInsertZonePositions]);

  const handleInsert = useCallback(
    (index: number, word: string) => {
      console.log('=== INSERT WORD ===');
      console.log('Word:', word);
      console.log('At index:', index);
      console.log('Current sentence:', sentence);

      if (usedWords.has(word)) {
        console.log('Word already used:', word);
        return;
      }
      if (sentence.length >= 10) {
        console.log('Sentence too long');
        return;
      }

      // Sử dụng functional update để đảm bảo cập nhật dựa trên state mới nhất
      setSentence(prevSentence => {
        const newSentence = [...prevSentence];
        newSentence.splice(index, 0, word);
        console.log('New sentence:', newSentence);
        return newSentence;
      });

      setUsedWords(prev => new Set([...prev, word]));
      setDraggableWords(prev => prev.filter(w => w !== word));
    },
    [usedWords],
  );

  const handleDragStart = useCallback(() => {
    setActiveZoneIndex(null);
  }, []);

  const handleDragMove = useCallback(
    (x: number, y: number) => {
      const zones = insertZonesRef.current;

      if (zones.length === 0) return;

      const closestZone = zones.reduce(
        (closest: InsertZonePosition, zone: InsertZonePosition) => {
          const dx = Math.abs(zone.x - x);
          const dy = Math.abs(zone.y - y);
          const distance = Math.sqrt(dx * dx + dy * dy);
          return distance < (closest.distance || Infinity)
            ? {...zone, distance}
            : closest;
        },
        {x: 0, y: 0, index: 0, distance: Infinity},
      );

      // Increased detection radius for smoother interaction
      const detectionRadius = 80;
      if (closestZone.distance && closestZone.distance < detectionRadius) {
        if (activeZoneIndex !== closestZone.index) {
          setActiveZoneIndex(closestZone.index);

          // Trigger word expansion animation
          if (closestZone.index < sentence.length) {
            setIsInserting(true);
            setInsertingAtIndex(closestZone.index);

            // Animate sentence container height
            Animated.timing(sentenceContainerHeight, {
              toValue: 100, // Expand height
              duration: 200,
              easing: Easing.out(Easing.quad),
              useNativeDriver: false,
            }).start();
          }
        }
      } else {
        if (activeZoneIndex !== null) {
          setActiveZoneIndex(null);

          // Reset expansion animation
          setIsInserting(false);
          setInsertingAtIndex(-1);

          // Reset sentence container height
          Animated.timing(sentenceContainerHeight, {
            toValue: 80, // Original height
            duration: 200,
            easing: Easing.out(Easing.quad),
            useNativeDriver: false,
          }).start();
        }
      }
    },
    [activeZoneIndex, sentence.length, sentenceContainerHeight],
  );

  const handleDragEnd = useCallback(
    (word: string, x: number, y: number) => {
      const zones = insertZonesRef.current;

      if (zones.length === 0) return;

      const closestZone = zones.reduce(
        (closest: InsertZonePosition, zone: InsertZonePosition) => {
          const dx = Math.abs(zone.x - x);
          const dy = Math.abs(zone.y - y);
          const distance = Math.sqrt(dx * dx + dy * dy);
          return distance < (closest.distance || Infinity)
            ? {...zone, distance}
            : closest;
        },
        {x: 0, y: 0, index: 0, distance: Infinity},
      );

      if (closestZone.distance && closestZone.distance < 50) {
        if (usedWords.has(word)) return;

        setSentence(prevSentence => {
          const newSentence = [...prevSentence];
          newSentence.splice(closestZone.index, 0, word);
          return newSentence;
        });

        setUsedWords(prev => new Set([...prev, word]));
        setDraggableWords(prev => prev.filter(w => w !== word));
      }

      setActiveZoneIndex(null);

      // Reset expansion animation after drop
      setIsInserting(false);
      setInsertingAtIndex(-1);

      // Reset sentence container height
      Animated.timing(sentenceContainerHeight, {
        toValue: 80,
        duration: 300,
        easing: Easing.out(Easing.back(1.1)),
        useNativeDriver: false,
      }).start();
    },
    [usedWords, sentenceContainerHeight],
  );

  const handleSubmit = useCallback(() => {
    setIsSubmitted(true);
  }, []);

  const resetSentence = useCallback(() => {
    setSentence(['Tôi', 'ăn', 'cơm']);
    setUsedWords(new Set());
    setDraggableWords(['đang', 'sẽ', 'đã', 'rất', 'ngon']);
    setIsSubmitted(false);

    // Reset animation states
    setIsInserting(false);
    setInsertingAtIndex(-1);
    setActiveZoneIndex(null);

    // Reset sentence container height
    sentenceContainerHeight.setValue(80);
  }, [sentenceContainerHeight]);

  // Refs for drop zones positions
  const {width: screenWidth} = Dimensions.get('window');

  const hiddenInputRef = useRef<TextInput | null>(null);

  useEffect(() => {
    startGame();
    Keyboard.addListener('keyboardDidShow', () => {
      setIsShowKeyboard(true);
    });
    Keyboard.addListener('keyboardDidHide', () => {
      setIsShowKeyboard(false);
      hiddenInputRef.current?.blur();
    });
    return () => {
      if (hiddenInputRef.current) {
        hiddenInputRef.current.focus();
      }
    };
  }, []);

  useEffect(() => {
    if (currentLives === 0) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);
  useEffect(() => {
    if (isWinLevel) {
      winGame();
    }
  }, [isWinLevel]);

  const startGame = () => {
    resetQuestion();
    gameHook.restartGame();
    dhbcHook.startGame();
  };

  // Thua
  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // Thắng
  const winGame = () => {
    gameHook.setData({stateName: 'gem', value: gem + 30});
    gameHook.setData({stateName: 'cup', value: cup + 10});
  };

  // Reset câu hỏi
  const resetQuestion = () => {
    setIsCorrect(false);
    setIsError(false);
  };

  // Sử dụng gợi ý
  const useHint = () => {
    gameHook.setData({stateName: 'gem', value: gem - 10});
    setShowModelConfirm(false);
    setShowHintModel(true);
  };
  return (
    <ImageBackground
      source={require('./assets/backgroundGame.png')}
      style={styles.backgroundImage}
      resizeMode="cover">
      <SafeAreaView edges={['top']} />

      <View style={styles.container}>
        {/* Header */}
        <HeadGame
          isShowSuggest={true}
          onUseHint={() => setShowModelConfirm(true)}
          timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
        />
        {!isShowKeyboard ? (
          <View>
            <LineProgressBar
              progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Lives
                totalLives={totalLives}
                currentLives={currentLives}></Lives>
              <CountBadge
                current={questionDone}
                total={totalQuestion}></CountBadge>
            </View>
          </View>
        ) : null}
        {/* Game Content */}
        <View style={styles.gameContent}>
          <View style={styles.questionContainer}>
            <Text style={styles.text}>{currentQuestion?.text}</Text>
          </View>
          <GestureHandlerRootView style={{flex: 1}}>
            <View style={styles.container}>
              <View style={styles.mainContent}>
                <Animated.View style={[styles.plateContainer]}>
                  <ImageBackground
                    source={require('./assets/plate.png')}
                    style={styles.plateBackground}
                    resizeMode="stretch">
                    <View style={styles.row}>
                      {sentence.map((word, index) => (
                        <View key={`slot-${index}`} style={styles.slotWrap}>
                          <InsertZone
                            ref={ref => {
                              if (ref) {
                                insertZoneRefs.current[index] = ref;
                              }
                            }}
                            index={index}
                            onReceiveDragDrop={word =>
                              handleInsert(index, word)
                            }
                            isActive={activeZoneIndex === index}
                            onDropSuccess={() =>
                              console.log(`Word dropped at index ${index}`)
                            }
                          />
                          <AnimatedWord
                            word={word}
                            index={index}
                            isSubmitted={isSubmitted}
                            isInserting={isInserting}
                            insertIndex={insertingAtIndex}
                          />
                        </View>
                      ))}
                      <InsertZone
                        ref={ref => {
                          if (ref) {
                            insertZoneRefs.current[sentence.length] = ref;
                          }
                        }}
                        index={sentence.length}
                        onReceiveDragDrop={word =>
                          handleInsert(sentence.length, word)
                        }
                        isActive={activeZoneIndex === sentence.length}
                        onDropSuccess={() => console.log(`Word dropped at end`)}
                      />
                    </View>
                  </ImageBackground>
                </Animated.View>

                <View style={styles.wordBank}>
                  {draggableWords.map((word, index) => (
                    <DraggableWord
                      key={`drag-${index}-${word}`}
                      word={word}
                      used={usedWords.has(word)}
                      onDragEnd={handleDragEnd}
                      onDragStart={handleDragStart}
                      onDragMove={handleDragMove}
                    />
                  ))}
                </View>

                <View style={styles.buttonContainer}>
                  <TouchableOpacity
                    style={[styles.button, styles.resetButton]}
                    onPress={resetSentence}>
                    <Text style={styles.buttonText}>Làm lại</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.button, styles.submitButton]}
                    onPress={handleSubmit}
                    disabled={isSubmitted}>
                    <Text style={styles.buttonText}>Kiểm tra đáp án</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </GestureHandlerRootView>

          <View>
            <BottomGame resetGame={startGame} />
          </View>
        </View>
        <View style={{zIndex: 1000}}>
          <ModelConfirm
            isShow={showModelConfirm}
            closeModal={() => setShowModelConfirm(false)}
            onConfirm={useHint}
          />
          <HintModel
            isShow={showHintModel}
            closeModal={() => setShowHintModel(false)}
            text={currentQuestion.hint}
          />
          <GameOverModal
            visible={isGameOver}
            onClose={() => {}}
            restartGame={startGame}
            message={messageGameOver}
            isTimeOut={false}
          />
          <ModelDoneLevel
            visible={isWinLevel}
            onNextLevel={startGame}
            currentGem={gem - 30}
            currentCup={cup - 10}
            gemAdd={30}
            cupAdd={10}
          />
        </View>
      </View>
      <SafeAreaView edges={['bottom']} />
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
    marginHorizontal: 16,
  },
  mainContent: {
    flex: 1,
    width: '100%',
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
  },
  questionContainer: {
    width: Dimensions.get('window').width - 32,
    height: undefined,
    minHeight: 65,
    marginBottom: 20,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    backgroundColor: '#FCF8E8',
    justifyContent: 'center',
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
  },
  plateContainer: {
    width: Dimensions.get('window').width - 32,
    height: 200,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
  },
  plateBackground: {
    width: Dimensions.get('window').width - 32,
    height: 200,
    paddingHorizontal: 20,
    paddingVertical: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sentenceContainer: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    padding: 16,
    minHeight: 80,
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '100%',
    padding: 16,
  },
  slotWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    flexShrink: 0,
  },
  insertZone: {
    width: 2,
    height: 2,
    marginHorizontal: 4,
  },
  insertZoneInner: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f8f9fa',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: '#dee2e6',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  word: {
    padding: 8,
    backgroundColor: '#F1D1A6',
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#FFFFFFFF',
    minWidth: 40,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 9,
  },
  wordText: {
    fontSize: 16,
    color: '#112164',
    fontWeight: '500',
    textAlign: 'center',
  },
  wordSubmitted: {
    backgroundColor: '#c8e6c9',
    borderColor: '#4caf50',
  },
  wordTextSubmitted: {
    color: '#2e7d32',
  },
  wordBank: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
    padding: 10,
    width: '100%',
  },
  draggable: {
    padding: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
    backgroundColor: '#F1D1A6',
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#FFFFFFFF',
    minWidth: 40,
  },
  dragText: {
    fontSize: 16,
    color: '#112164',
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
    paddingVertical: 10,
    width: '100%',
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    minWidth: 120,
  },
  resetButton: {
    backgroundColor: '#f44336',
  },
  submitButton: {
    backgroundColor: '#4caf50',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  usedDraggable: {
    backgroundColor: '#bdbdbd',
    opacity: 0.5,
  },
  insertZoneActive: {
    backgroundColor: '#e3f2fd',
    borderWidth: 3,
    borderColor: '#112164',
    borderStyle: 'solid',
    shadowColor: '#112164',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.4,
    shadowRadius: 6,
    elevation: 8,
  },
  gameContent: {
    marginTop: 16,
    flex: 1,
    alignItems: 'center',
  },
  imageContainer: {
    width: '90%',
    height: 200,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
  },

  answerContainer: {
    position: 'relative',
    marginTop: 32,
    maxWidth: '70%',
    minWidth: 200,
    backgroundColor: 'white',
    borderRadius: 15,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  answerContainerError: {
    borderColor: '#FF6B6B',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerContainerCorrect: {
    borderColor: '#2EB553',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',

    backgroundColor: '#FCF8E8',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginTop: 5,
  },
  correctText: {
    fontSize: 12,
    textAlign: 'center',

    backgroundColor: '#E8F8FC',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#2EB553',
    fontWeight: 'bold',
    marginTop: 5,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  skipButton: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 15,
    flex: 1,
    marginRight: 10,
  },
  skipButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  checkButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 15,
    flex: 1,
    marginLeft: 10,
  },
  checkButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  hiddenInput: {
    width: 0, // Chiều rộng 0
    height: 0, // Chiều cao 0
    opacity: 0, // Hoàn toàn trong suốt
    position: 'absolute', // Không chiếm không gian trong layout
    // Để đảm bảo nó thực sự không nhìn thấy và không tương tác ngoài ý muốn:
    top: -10000, // Đẩy ra rất xa màn hình
    left: -10000,
  },
  // Drag and Drop Game Styles
  questionHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#555',
  },
  dropZonesContainer: {
    width: '100%',
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  dropZonesRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
  },
  dropZone: {
    minWidth: 80,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 4,
    borderWidth: 2,
  },
  dropZoneEmpty: {
    backgroundColor: '#f0f0f0',
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  dropZoneFilled: {
    backgroundColor: '#e8f5e8',
    borderColor: '#4CAF50',
    borderStyle: 'solid',
  },
  dropZoneText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  dropZoneButton: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  availableWordsContainer: {
    width: '100%',
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  wordsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    paddingVertical: 10,
  },
  wordItem: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    margin: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    minWidth: 80,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  wordItemActive: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196F3',
    transform: [{scale: 1.05}],
  },
  //   wordText: {
  //     fontSize: 14,
  //     fontWeight: '500',
  //     color: '#333',
  //   },
});

export default SakuXayTo;
